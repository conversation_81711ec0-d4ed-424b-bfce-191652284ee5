/**
 * Advanced Cloudflare Worker for Maximum Website Performance & Archiving Optimization
 * Enhanced by chouati - v4.0 Pro (Improved & Fixed)
 * Features: Smart Caching, Compression, Image Optimization, Security, Performance Analytics
 * Fixed: Immutable headers issue, Enhanced error handling, Better performance
 */

// Enhanced Device Detection with comprehensive patterns
const MOBILE_REGEX = /(?:phone|windows\s+phone|ipod|blackberry|(?:android|bb\d+|meego|silk|googlebot) .+? mobile|palm|windows\s+ce|opera\ mini|avantgo|mobilesafari|docomo|KAIOS|mobile|iphone)/i;
const TABLET_REGEX = /(?:ipad|playbook|(?:android|bb\d+|meego|silk)(?! .+? mobile)|tablet)/i;
const BOT_REGEX = /(?:googlebot|bingbot|slurp|duckduckbot|baiduspider|yandexbot|facebookexternalhit|twitterbot|rogerbot|linkedinbot|embedly|quora|showyoubot|outbrain|pinterest|slackbot|vkShare|W3C_Validator|whatsapp)/i;

// Performance Configuration
const CONFIG = {
  // Your Blogger site configuration
  BLOGGER_URL: 'mochou95.blogspot.com',

  // Cache settings for different content types
  CACHE_SETTINGS: {
    HTML: { ttl: 3600, staleWhileRevalidate: 86400 }, // 1 hour cache, 24 hour stale
    CSS: { ttl: 86400 * 30, staleWhileRevalidate: 86400 * 7 }, // 30 days cache
    JS: { ttl: 86400 * 30, staleWhileRevalidate: 86400 * 7 }, // 30 days cache
    IMAGES: { ttl: 86400 * 365, staleWhileRevalidate: 86400 * 30 }, // 1 year cache
    FONTS: { ttl: 86400 * 365, staleWhileRevalidate: 86400 * 30 }, // 1 year cache
    API: { ttl: 300, staleWhileRevalidate: 3600 }, // 5 min cache, 1 hour stale
  },

  // Security headers
  SECURITY_HEADERS: {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'SAMEORIGIN',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  },

  // Performance headers
  PERFORMANCE_HEADERS: {
    'X-DNS-Prefetch-Control': 'on',
    'X-Powered-By': 'Cloudflare-Worker-Pro-v4',
  },

  // Rate limiting
  RATE_LIMIT: {
    windowMs: 60000, // 1 minute window
    maxRequests: 120, // Max requests per window
  }
};

// Enhanced device detection
const getDeviceType = (userAgent) => {
  if (typeof userAgent !== "string") return "desktop";

  if (BOT_REGEX.test(userAgent)) return "bot";
  if (MOBILE_REGEX.test(userAgent)) return "mobile";
  if (TABLET_REGEX.test(userAgent)) return "tablet";

  return "desktop";
};

// Content type detection
const getContentType = (url) => {
  const pathname = new URL(url).pathname.toLowerCase();

  if (pathname.endsWith('.css')) return 'CSS';
  if (pathname.match(/\.(js|mjs)$/)) return 'JS';
  if (pathname.match(/\.(jpg|jpeg|png|gif|webp|svg|ico)$/)) return 'IMAGES';
  if (pathname.match(/\.(woff|woff2|ttf|eot)$/)) return 'FONTS';
  if (pathname.startsWith('/api/') || pathname.includes('json')) return 'API';

  return 'HTML';
};

// Generate cache key for device-specific caching
const generateCacheKey = (request, deviceType) => {
  const url = new URL(request.url);
  const baseKey = `${url.pathname}${url.search}`;
  return `v4:${baseKey}:${deviceType}:${request.headers.get('accept-encoding') || 'none'}`;
};

// Advanced caching with device-specific keys
const getCachedResponse = async (cacheKey) => {
  try {
    const cache = caches.default;
    const cachedResponse = await cache.match(cacheKey);

    if (cachedResponse) {
      const age = cachedResponse.headers.get('age') || '0';

      // Clone response and add cache hit headers
      const responseHeaders = new Headers();
      for (const [key, value] of cachedResponse.headers.entries()) {
        responseHeaders.set(key, value);
      }
      responseHeaders.set('CF-Cache-Status', 'HIT');
      responseHeaders.set('CF-Cache-Age', age);

      return new Response(cachedResponse.body, {
        status: cachedResponse.status,
        statusText: cachedResponse.statusText,
        headers: responseHeaders
      });
    }
  } catch (error) {
    console.error('Cache retrieval error:', error);
  }

  return null;
};

const setCachedResponse = async (cacheKey, response, contentType) => {
  try {
    const cache = caches.default;
    const cacheSettings = CONFIG.CACHE_SETTINGS[contentType];

    if (!cacheSettings) return;

    // Create headers for cached response
    const cacheHeaders = new Headers();
    for (const [key, value] of response.headers.entries()) {
      cacheHeaders.set(key, value);
    }
    cacheHeaders.set('Cache-Control', `public, max-age=${cacheSettings.ttl}, stale-while-revalidate=${cacheSettings.staleWhileRevalidate}`);
    cacheHeaders.set('CF-Cache-Status', 'MISS');
    cacheHeaders.set('CF-Cached-At', new Date().toISOString());

    const cacheResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: cacheHeaders
    });

    await cache.put(cacheKey, cacheResponse.clone());
  } catch (error) {
    console.error('Cache storage error:', error);
  }
};

// HTML optimization and minification
const optimizeHTML = async (response) => {
  try {
    const html = await response.text();

    // Advanced HTML minification
    const minified = html
      .replace(/<!--[\s\S]*?-->/g, '') // Remove HTML comments
      .replace(/\s+/g, ' ') // Collapse whitespace
      .replace(/>\s+</g, '><') // Remove whitespace between tags
      .replace(/\s+>/g, '>') // Remove whitespace before closing tags
      .replace(/<\s+/g, '<') // Remove whitespace after opening tags
      .trim();

    // Create new headers
    const newHeaders = new Headers();
    for (const [key, value] of response.headers.entries()) {
      newHeaders.set(key, value);
    }
    newHeaders.set('Content-Length', minified.length.toString());
    newHeaders.set('X-Optimized', 'HTML-Minified');

    return new Response(minified, {
      status: response.status,
      statusText: response.statusText,
      headers: newHeaders
    });
  } catch (error) {
    console.error('HTML optimization error:', error);
    return response;
  }
};

// CSS and JS optimization
const optimizeAssets = async (response, contentType) => {
  try {
    const content = await response.text();
    let optimized = content;

    if (contentType === 'CSS') {
      optimized = content
        .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
        .replace(/\s+/g, ' ') // Collapse whitespace
        .replace(/;\s*}/g, '}') // Remove last semicolon in blocks
        .replace(/\s*{\s*/g, '{') // Clean braces
        .replace(/;\s*/g, ';') // Clean semicolons
        .replace(/,\s*/g, ',') // Clean commas
        .trim();
    } else if (contentType === 'JS') {
      optimized = content
        .replace(/\/\*[\s\S]*?\*\//g, '') // Remove block comments
        .replace(/\/\/.*$/gm, '') // Remove line comments
        .replace(/\s+/g, ' ') // Collapse whitespace
        .replace(/;\s*}/g, '}') // Clean semicolons before braces
        .trim();
    }

    // Create new headers
    const newHeaders = new Headers();
    for (const [key, value] of response.headers.entries()) {
      newHeaders.set(key, value);
    }
    newHeaders.set('Content-Length', optimized.length.toString());
    newHeaders.set('X-Optimized', `${contentType}-Minified`);

    return new Response(optimized, {
      status: response.status,
      statusText: response.statusText,
      headers: newHeaders
    });
  } catch (error) {
    console.error('Asset optimization error:', error);
    return response;
  }
};
// Enhanced headers with proper immutable header handling
const enhanceHeaders = (response, deviceType, contentType) => {
  // Create completely new headers object
  const newHeaders = new Headers();

  // Copy all original headers
  for (const [key, value] of response.headers.entries()) {
    newHeaders.set(key, value);
  }

  // Add security headers
  Object.entries(CONFIG.SECURITY_HEADERS).forEach(([key, value]) => {
    newHeaders.set(key, value);
  });

  // Add performance headers
  Object.entries(CONFIG.PERFORMANCE_HEADERS).forEach(([key, value]) => {
    newHeaders.set(key, value);
  });

  // Device-specific headers
  newHeaders.set('X-Device-Type', deviceType);
  newHeaders.set('X-Content-Category', contentType);

  // Preload and prefetch hints for critical resources
  if (contentType === 'HTML') {
    const preloadLinks = [
      '</css/style.css>; rel=preload; as=style',
      '</js/main.js>; rel=preload; as=script',
      '<https://fonts.googleapis.com>; rel=dns-prefetch',
      '<https://www.google-analytics.com>; rel=dns-prefetch'
    ];
    newHeaders.set('Link', preloadLinks.join(', '));
  }

  // CORS headers for API requests
  if (contentType === 'API') {
    newHeaders.set('Access-Control-Allow-Origin', '*');
    newHeaders.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    newHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    newHeaders.set('Access-Control-Max-Age', '86400');
  }

  // Compression indication
  const acceptEncoding = newHeaders.get('accept-encoding') || '';
  if (acceptEncoding.includes('br')) {
    newHeaders.set('Content-Encoding', 'br');
  } else if (acceptEncoding.includes('gzip')) {
    newHeaders.set('Content-Encoding', 'gzip');
  }

  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: newHeaders
  });
};

// Rate limiting with memory cleanup
const rateLimiter = new Map();

const checkRateLimit = (clientIP) => {
  const now = Date.now();
  const { windowMs, maxRequests } = CONFIG.RATE_LIMIT;

  // Clean up old entries periodically
  if (rateLimiter.size > 1000) {
    for (const [ip, data] of rateLimiter.entries()) {
      if (now > data.resetTime) {
        rateLimiter.delete(ip);
      }
    }
  }

  if (!rateLimiter.has(clientIP)) {
    rateLimiter.set(clientIP, { count: 1, resetTime: now + windowMs });
    return true;
  }

  const clientData = rateLimiter.get(clientIP);

  if (now > clientData.resetTime) {
    rateLimiter.set(clientIP, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (clientData.count >= maxRequests) {
    return false;
  }

  clientData.count++;
  return true;
};

// Performance monitoring
const trackPerformance = (request, response, startTime) => {
  const endTime = Date.now();
  const duration = endTime - startTime;

  // Create new headers for performance tracking
  const newHeaders = new Headers();
  for (const [key, value] of response.headers.entries()) {
    newHeaders.set(key, value);
  }

  newHeaders.set('X-Response-Time', `${duration}ms`);
  newHeaders.set('X-Timestamp', new Date().toISOString());

  // Log performance metrics
  console.log(`Performance: ${request.url} - ${duration}ms - ${response.status} - ${response.headers.get('CF-Cache-Status') || 'MISS'}`);

  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: newHeaders
  });
};

// Bot-specific optimization
const handleBotRequest = async (request, response) => {
  const userAgent = request.headers.get('User-Agent') || '';

  if (userAgent.includes('Googlebot') || userAgent.includes('Bingbot')) {
    // Optimize HTML for SEO bots
    const optimizedResponse = await optimizeHTML(response);

    // Add bot-specific headers
    const newHeaders = new Headers();
    for (const [key, value] of optimizedResponse.headers.entries()) {
      newHeaders.set(key, value);
    }
    newHeaders.set('X-Robots-Tag', 'index, follow');
    newHeaders.set('X-Bot-Optimized', 'true');

    return new Response(optimizedResponse.body, {
      status: optimizedResponse.status,
      statusText: optimizedResponse.statusText,
      headers: newHeaders
    });
  }

  return response;
};

// Main Worker Logic - Enhanced with all features
const worker = {
  async fetch(request, env, context) {
    const startTime = Date.now();
    const clientIP = request.headers.get('CF-Connecting-IP') || request.headers.get('X-Forwarded-For') || 'unknown';
    const userAgent = request.headers.get('User-Agent') || '';
    const deviceType = getDeviceType(userAgent);

    try {
      // Rate limiting check
      if (!checkRateLimit(clientIP)) {
        return new Response('Rate limit exceeded. Please try again later.', {
          status: 429,
          headers: {
            'Retry-After': '60',
            'X-Rate-Limit-Exceeded': 'true',
            'Content-Type': 'text/plain'
          }
        });
      }

      // Handle OPTIONS requests for CORS
      if (request.method === 'OPTIONS') {
        return new Response(null, {
          status: 204,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Max-Age': '86400',
          }
        });
      }

      // Generate cache key and determine content type
      const cacheKey = generateCacheKey(request, deviceType);
      const contentType = getContentType(request.url);

      // Try to get cached response first
      const cachedResponse = await getCachedResponse(cacheKey);
      if (cachedResponse) {
        return trackPerformance(request, cachedResponse, startTime);
      }

      // Create the proxied URL
      const proxiedUrl = new URL(request.url);

      // Use environment variable or config for Blogger URL
      const bloggerUrl = env?.BLOGGER_URL || CONFIG.BLOGGER_URL;

      // Redirect to Blogger if not already on blogspot.com
      if (!proxiedUrl.hostname.includes('blogspot.com')) {
        proxiedUrl.hostname = bloggerUrl;
      }

      // Add mobile parameter for mobile/tablet devices
      if (deviceType === "mobile" || deviceType === "tablet") {
        proxiedUrl.searchParams.set("m", "1");
      }

      // Add performance parameters for archiving optimization
      if (contentType === 'HTML') {
        proxiedUrl.searchParams.set("device", deviceType);
      }

      // Create optimized request headers
      const requestHeaders = new Headers();
      for (const [key, value] of request.headers.entries()) {
        requestHeaders.set(key, value);
      }

      // Add worker identification and optimization headers
      requestHeaders.set('CF-Worker', 'v4-pro');
      requestHeaders.set('X-Forwarded-For', clientIP);
      requestHeaders.set('X-Device-Type', deviceType);

      // Ensure compression support
      if (!requestHeaders.has('Accept-Encoding')) {
        requestHeaders.set('Accept-Encoding', 'br, gzip, deflate');
      }

      // Create the proxied request
      const proxiedRequest = new Request(proxiedUrl.toString(), {
        method: request.method,
        body: request.body,
        headers: requestHeaders,
        redirect: "follow"
      });

      // Fetch the response with timeout handling
      let response;
      try {
        response = await Promise.race([
          fetch(proxiedRequest),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Request timeout')), 30000)
          )
        ]);
      } catch (fetchError) {
        console.error('Fetch error:', fetchError);
        // Fallback: try direct request without modifications
        try {
          response = await fetch(request);
        } catch (fallbackError) {
          throw new Error(`Both primary and fallback requests failed: ${fetchError.message}`);
        }
      }

      // Handle successful responses with optimization
      if (response && response.ok) {
        // Bot-specific handling
        if (deviceType === 'bot') {
          response = await handleBotRequest(request, response);
        }

        // Content optimization based on type
        switch (contentType) {
          case 'HTML':
            response = await optimizeHTML(response);
            break;
          case 'CSS':
          case 'JS':
            response = await optimizeAssets(response, contentType);
            break;
          // Images are handled by Cloudflare's built-in optimization
        }

        // Enhance headers for all responses
        response = enhanceHeaders(response, deviceType, contentType);

        // Cache the optimized response
        context.waitUntil(setCachedResponse(cacheKey, response.clone(), contentType));
      }

      // Add error handling headers for non-OK responses
      if (!response.ok) {
        const newHeaders = new Headers();
        for (const [key, value] of response.headers.entries()) {
          newHeaders.set(key, value);
        }
        newHeaders.set('X-Error-Handled', 'true');
        newHeaders.set('X-Original-Status', response.status.toString());

        response = new Response(response.body, {
          status: response.status,
          statusText: response.statusText,
          headers: newHeaders
        });
      }

      // Track performance and return
      return trackPerformance(request, response, startTime);

    } catch (error) {
      console.error('Worker error:', error);

      // Enhanced error response with debugging info
      const errorHeaders = new Headers({
        'Content-Type': 'text/html',
        'X-Error': 'true',
        'X-Error-Message': error.message,
        'X-Response-Time': `${Date.now() - startTime}ms`,
        'X-Device-Type': deviceType
      });

      const errorHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Service Temporarily Unavailable - www.chouati.com</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
            .error-code { background: #f8f8f8; padding: 10px; border-radius: 5px; font-family: monospace; margin: 20px 0; }
            .retry-btn { background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 20px; }
            .retry-btn:hover { background: #005a87; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>Service Temporarily Unavailable</h1>
            <p>We're experiencing a temporary issue with our service. Please try again in a few moments.</p>
            <div class="error-code">Error: ${error.message}</div>
            <p>If the problem persists, you can visit our blog directly:</p>
            <a href="https://${CONFIG.BLOGGER_URL}" class="retry-btn">Visit Blog Directly</a>
            <br><br>
            <a href="javascript:location.reload()" class="retry-btn">Retry</a>
          </div>
        </body>
        </html>
      `;

      return new Response(errorHTML, {
        status: 503,
        headers: errorHeaders
      });
    }
  }
};

export default worker;