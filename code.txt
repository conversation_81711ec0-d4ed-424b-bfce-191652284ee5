/**
 * this code made by chouati
 * v2 
 */

const MOBILE_REGEX = /(?:phone|windows\s+phone|ipod|blackberry|(?:android|bb\d+|meego|silk|googlebot) .+? mobile|palm|windows\s+ce|opera\ mini|avantgo|mobilesafari|docomo|KAIOS)/i;
const TABLET_REGEX = /(?:ipad|playbook|(?:android|bb\d+|meego|silk)(?! .+? mobile))/i;

const getDeviceType = (userAgent) => {
  if (typeof userAgent === "string") {
    if (MOBILE_REGEX.test(userAgent)) {
      return "mobile";
    }

    if (TABLET_REGEX.test(userAgent)) {
      return "tablet";
    }
  }

  return "desktop";
}

const worker = {
  async fetch(request, env, context) {
    // Get the device type from user-agent header
    const deviceType = getDeviceType(request.headers.get("User-Agent"));

    const proxiedUrl = new URL(request.url);
    if (deviceType !== "desktop") {
      proxiedUrl.searchParams.set("m", "1")
    }

    const proxiedRequest = new Request(proxiedUrl, {
      method: request.method,
      body: request.body,
      headers: request.headers,
      redirect: "follow"
    });

    const proxiedResponse = await fetch(proxiedRequest);

    const response = new Response(proxiedResponse.body, proxiedResponse);

    return response;
  }
}

export default worker;
