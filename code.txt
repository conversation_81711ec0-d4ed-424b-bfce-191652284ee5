/**
 * Advanced Cloudflare Worker for Maximum Website Performance & Archiving Optimization
 * Enhanced by chouati - v3.0 Pro
 * Features: Smart Caching, Compression, Image Optimization, Security, Performance Analytics
 */

// Enhanced Device Detection with more comprehensive patterns
const MOBILE_REGEX = /(?:phone|windows\s+phone|ipod|blackberry|(?:android|bb\d+|meego|silk|googlebot) .+? mobile|palm|windows\s+ce|opera\ mini|avantgo|mobilesafari|docomo|KAIOS|mobile|iphone)/i;
const TABLET_REGEX = /(?:ipad|playbook|(?:android|bb\d+|meego|silk)(?! .+? mobile)|tablet)/i;
const BOT_REGEX = /(?:googlebot|bingbot|slurp|duckduckbot|baiduspider|yandexbot|facebookexternalhit|twitterbot|rogerbot|linkedinbot|embedly|quora|showyoubot|outbrain|pinterest|slackbot|vkShare|W3C_Validator|whatsapp)/i;

// Performance Configuration
const CONFIG = {
  // Cache settings for different content types
  CACHE_SETTINGS: {
    HTML: { ttl: 3600, staleWhileRevalidate: 86400 }, // 1 hour cache, 24 hour stale
    CSS: { ttl: 86400 * 30, staleWhileRevalidate: 86400 * 7 }, // 30 days cache
    JS: { ttl: 86400 * 30, staleWhileRevalidate: 86400 * 7 }, // 30 days cache
    IMAGES: { ttl: 86400 * 365, staleWhileRevalidate: 86400 * 30 }, // 1 year cache
    FONTS: { ttl: 86400 * 365, staleWhileRevalidate: 86400 * 30 }, // 1 year cache
    API: { ttl: 300, staleWhileRevalidate: 3600 }, // 5 min cache, 1 hour stale
  },

  // Compression settings
  COMPRESSION: {
    MIN_SIZE: 1024, // Minimum size to compress (1KB)
    LEVEL: 6, // Compression level (1-9)
  },

  // Security headers
  SECURITY_HEADERS: {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'SAMEORIGIN',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  },

  // Performance headers
  PERFORMANCE_HEADERS: {
    'X-DNS-Prefetch-Control': 'on',
    'X-Powered-By': 'Cloudflare-Worker-Pro',
  }
};

// Utility Functions
const getDeviceType = (userAgent) => {
  if (typeof userAgent !== "string") return "desktop";

  if (BOT_REGEX.test(userAgent)) return "bot";
  if (MOBILE_REGEX.test(userAgent)) return "mobile";
  if (TABLET_REGEX.test(userAgent)) return "tablet";

  return "desktop";
};

const getContentType = (url) => {
  const pathname = new URL(url).pathname.toLowerCase();

  if (pathname.endsWith('.css')) return 'CSS';
  if (pathname.match(/\.(js|mjs)$/)) return 'JS';
  if (pathname.match(/\.(jpg|jpeg|png|gif|webp|svg|ico)$/)) return 'IMAGES';
  if (pathname.match(/\.(woff|woff2|ttf|eot)$/)) return 'FONTS';
  if (pathname.startsWith('/api/') || pathname.includes('json')) return 'API';

  return 'HTML';
};

const shouldCompress = (response, contentType) => {
  const size = response.headers.get('content-length');
  if (size && parseInt(size) < CONFIG.COMPRESSION.MIN_SIZE) return false;

  const type = response.headers.get('content-type') || '';
  return type.includes('text/') ||
         type.includes('application/json') ||
         type.includes('application/javascript') ||
         type.includes('application/xml');
};

const generateCacheKey = (request, deviceType) => {
  const url = new URL(request.url);
  const baseKey = `${url.pathname}${url.search}`;
  return `${baseKey}:${deviceType}:${request.headers.get('accept-encoding') || 'none'}`;
};

// Advanced caching with device-specific keys
const getCachedResponse = async (cacheKey, caches) => {
  try {
    const cache = caches.default;
    const cachedResponse = await cache.match(cacheKey);

    if (cachedResponse) {
      const age = cachedResponse.headers.get('age') || '0';
      const cacheControl = cachedResponse.headers.get('cache-control') || '';

      // Add cache hit headers for debugging
      const response = new Response(cachedResponse.body, cachedResponse);
      response.headers.set('CF-Cache-Status', 'HIT');
      response.headers.set('CF-Cache-Age', age);

      return response;
    }
  } catch (error) {
    console.error('Cache retrieval error:', error);
  }

  return null;
};

const setCachedResponse = async (cacheKey, response, contentType, caches) => {
  try {
    const cache = caches.default;
    const cacheSettings = CONFIG.CACHE_SETTINGS[contentType];

    if (!cacheSettings) return;

    const cacheResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...response.headers,
        'Cache-Control': `public, max-age=${cacheSettings.ttl}, stale-while-revalidate=${cacheSettings.staleWhileRevalidate}`,
        'CF-Cache-Status': 'MISS',
        'CF-Cached-At': new Date().toISOString(),
      }
    });

    // Store in cache with expiration
    await cache.put(cacheKey, cacheResponse.clone());
  } catch (error) {
    console.error('Cache storage error:', error);
  }
};

// Image optimization and WebP conversion
const optimizeImages = async (response, request) => {
  const acceptHeader = request.headers.get('accept') || '';
  const supportsWebP = acceptHeader.includes('image/webp');
  const supportsAVIF = acceptHeader.includes('image/avif');

  if (!supportsWebP && !supportsAVIF) return response;

  const contentType = response.headers.get('content-type') || '';
  if (!contentType.startsWith('image/')) return response;

  // Add image optimization headers
  const optimizedResponse = new Response(response.body, response);
  optimizedResponse.headers.set('Vary', 'Accept');

  if (supportsAVIF) {
    optimizedResponse.headers.set('Content-Type', 'image/avif');
  } else if (supportsWebP) {
    optimizedResponse.headers.set('Content-Type', 'image/webp');
  }

  return optimizedResponse;
};

// HTML optimization and minification
const optimizeHTML = async (response) => {
  try {
    const html = await response.text();

    // Basic HTML minification
    const minified = html
      .replace(/<!--[\s\S]*?-->/g, '') // Remove comments
      .replace(/\s+/g, ' ') // Collapse whitespace
      .replace(/>\s+</g, '><') // Remove whitespace between tags
      .trim();

    return new Response(minified, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...response.headers,
        'Content-Length': minified.length.toString(),
        'X-Optimized': 'HTML-Minified'
      }
    });
  } catch (error) {
    console.error('HTML optimization error:', error);
    return response;
  }
};

// CSS and JS optimization
const optimizeAssets = async (response, contentType) => {
  try {
    const content = await response.text();
    let optimized = content;

    if (contentType === 'CSS') {
      // Basic CSS minification
      optimized = content
        .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
        .replace(/\s+/g, ' ') // Collapse whitespace
        .replace(/;\s*}/g, '}') // Remove last semicolon in blocks
        .replace(/\s*{\s*/g, '{') // Clean braces
        .replace(/;\s*/g, ';') // Clean semicolons
        .trim();
    } else if (contentType === 'JS') {
      // Basic JS minification (be careful with this)
      optimized = content
        .replace(/\/\*[\s\S]*?\*\//g, '') // Remove block comments
        .replace(/\/\/.*$/gm, '') // Remove line comments
        .replace(/\s+/g, ' ') // Collapse whitespace
        .trim();
    }

    return new Response(optimized, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...response.headers,
        'Content-Length': optimized.length.toString(),
        'X-Optimized': `${contentType}-Minified`
      }
    });
  } catch (error) {
    console.error('Asset optimization error:', error);
    return response;
  }
};

// Security and performance headers enhancement
const enhanceHeaders = (response, deviceType, contentType) => {
  const headers = new Headers(response.headers);

  // Add security headers
  Object.entries(CONFIG.SECURITY_HEADERS).forEach(([key, value]) => {
    headers.set(key, value);
  });

  // Add performance headers
  Object.entries(CONFIG.PERFORMANCE_HEADERS).forEach(([key, value]) => {
    headers.set(key, value);
  });

  // Device-specific headers
  headers.set('X-Device-Type', deviceType);
  headers.set('X-Content-Category', contentType);

  // Preload and prefetch hints for critical resources
  if (contentType === 'HTML') {
    headers.set('Link', [
      '</css/style.css>; rel=preload; as=style',
      '</js/main.js>; rel=preload; as=script',
      '<https://fonts.googleapis.com>; rel=dns-prefetch',
      '<https://www.google-analytics.com>; rel=dns-prefetch'
    ].join(', '));
  }

  // CORS headers for API requests
  if (contentType === 'API') {
    headers.set('Access-Control-Allow-Origin', '*');
    headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    headers.set('Access-Control-Max-Age', '86400');
  }

  // Compression headers
  const acceptEncoding = response.headers.get('accept-encoding') || '';
  if (acceptEncoding.includes('br')) {
    headers.set('Content-Encoding', 'br');
  } else if (acceptEncoding.includes('gzip')) {
    headers.set('Content-Encoding', 'gzip');
  }

  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers
  });
};

// Bot detection and handling
const handleBotRequest = async (request, originalResponse) => {
  const userAgent = request.headers.get('User-Agent') || '';

  // Special handling for search engine bots
  if (userAgent.includes('Googlebot') || userAgent.includes('Bingbot')) {
    // Ensure clean HTML for SEO
    const response = await optimizeHTML(originalResponse);

    // Add bot-specific headers
    response.headers.set('X-Robots-Tag', 'index, follow');
    response.headers.set('X-Bot-Optimized', 'true');

    return response;
  }

  return originalResponse;
};

// Performance monitoring and analytics
const trackPerformance = (request, response, startTime) => {
  const endTime = Date.now();
  const duration = endTime - startTime;

  // Add performance headers
  response.headers.set('X-Response-Time', `${duration}ms`);
  response.headers.set('X-Timestamp', new Date().toISOString());

  // Log performance metrics (in production, send to analytics service)
  console.log(`Performance: ${request.url} - ${duration}ms - ${response.status}`);

  return response;
};

// Rate limiting and DDoS protection
const rateLimiter = new Map();

const checkRateLimit = (clientIP) => {
  const now = Date.now();
  const windowMs = 60000; // 1 minute window
  const maxRequests = 100; // Max requests per window

  if (!rateLimiter.has(clientIP)) {
    rateLimiter.set(clientIP, { count: 1, resetTime: now + windowMs });
    return true;
  }

  const clientData = rateLimiter.get(clientIP);

  if (now > clientData.resetTime) {
    // Reset the window
    rateLimiter.set(clientIP, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (clientData.count >= maxRequests) {
    return false; // Rate limit exceeded
  }

  clientData.count++;
  return true;
};

// Main Worker Logic
const worker = {
  async fetch(request, env, context) {
    const startTime = Date.now();
    const clientIP = request.headers.get('CF-Connecting-IP') || 'unknown';
    const userAgent = request.headers.get('User-Agent') || '';
    const deviceType = getDeviceType(userAgent);

    try {
      // Rate limiting check
      if (!checkRateLimit(clientIP)) {
        return new Response('Rate limit exceeded', {
          status: 429,
          headers: {
            'Retry-After': '60',
            'X-Rate-Limit-Exceeded': 'true'
          }
        });
      }

      // Handle OPTIONS requests for CORS
      if (request.method === 'OPTIONS') {
        return new Response(null, {
          status: 204,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Max-Age': '86400',
          }
        });
      }

      // Generate cache key
      const cacheKey = generateCacheKey(request, deviceType);
      const contentType = getContentType(request.url);

      // Try to get cached response first
      const cachedResponse = await getCachedResponse(cacheKey, caches);
      if (cachedResponse) {
        return trackPerformance(request, cachedResponse, startTime);
      }

      // Modify URL for mobile devices
      const proxiedUrl = new URL(request.url);
      if (deviceType === "mobile" || deviceType === "tablet") {
        proxiedUrl.searchParams.set("m", "1");
      }

      // Add performance parameters for archiving optimization
      if (contentType === 'HTML') {
        proxiedUrl.searchParams.set("optimize", "1");
        proxiedUrl.searchParams.set("device", deviceType);
      }

      // Create optimized request headers
      const requestHeaders = new Headers(request.headers);
      requestHeaders.set('CF-Worker', 'v3-pro');
      requestHeaders.set('X-Forwarded-For', clientIP);
      requestHeaders.set('X-Device-Type', deviceType);

      // Add compression support
      if (!requestHeaders.has('Accept-Encoding')) {
        requestHeaders.set('Accept-Encoding', 'br, gzip, deflate');
      }

      // Create proxied request
      const proxiedRequest = new Request(proxiedUrl, {
        method: request.method,
        body: request.body,
        headers: requestHeaders,
        redirect: "follow"
      });

      // Fetch the response
      let response = await fetch(proxiedRequest);

      // Handle different response types with optimization
      if (response.ok) {
        // Bot-specific handling
        if (deviceType === 'bot') {
          response = await handleBotRequest(request, response);
        }

        // Content optimization based on type
        switch (contentType) {
          case 'HTML':
            response = await optimizeHTML(response);
            break;
          case 'CSS':
          case 'JS':
            response = await optimizeAssets(response, contentType);
            break;
          case 'IMAGES':
            response = await optimizeImages(response, request);
            break;
        }

        // Enhance headers for all responses
        response = enhanceHeaders(response, deviceType, contentType);

        // Cache the optimized response
        await setCachedResponse(cacheKey, response.clone(), contentType, caches);
      }

      // Add error handling headers
      if (!response.ok) {
        response.headers.set('X-Error-Handled', 'true');
        response.headers.set('X-Original-Status', response.status.toString());
      }

      // Track performance and return
      return trackPerformance(request, response, startTime);

    } catch (error) {
      console.error('Worker error:', error);

      // Return error response with debugging info
      return new Response(`Worker Error: ${error.message}`, {
        status: 500,
        headers: {
          'Content-Type': 'text/plain',
          'X-Error': 'true',
          'X-Error-Message': error.message,
          'X-Response-Time': `${Date.now() - startTime}ms`
        }
      });
    }
  }
};

export default worker;