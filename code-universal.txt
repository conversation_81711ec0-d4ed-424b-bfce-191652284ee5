/**
 * Universal Cloudflare Worker for Any Blogger Site
 * Works with any domain - just set your Blogger URL in environment variables
 */

// Device Detection
const MOBILE_REGEX = /(?:phone|windows\s+phone|ipod|blackberry|(?:android|bb\d+|meego|silk|googlebot) .+? mobile|palm|windows\s+ce|opera\ mini|avantgo|mobilesafari|docomo|KAIOS|mobile|iphone)/i;
const TABLET_REGEX = /(?:ipad|playbook|(?:android|bb\d+|meego|silk)(?! .+? mobile)|tablet)/i;

const getDeviceType = (userAgent) => {
  if (typeof userAgent !== "string") return "desktop";
  
  if (MOBILE_REGEX.test(userAgent)) return "mobile";
  if (TABLET_REGEX.test(userAgent)) return "tablet";
  
  return "desktop";
};

// Main Worker
const worker = {
  async fetch(request, env, context) {
    try {
      // Get device type
      const userAgent = request.headers.get('User-Agent') || '';
      const deviceType = getDeviceType(userAgent);
      
      // Create the URL
      const url = new URL(request.url);
      
      // Method 1: Use environment variable (RECOMMENDED)
      // In Cloudflare Workers dashboard, set BLOGGER_URL = "yourblog.blogspot.com"
      if (env && env.BLOGGER_URL) {
        url.hostname = env.BLOGGER_URL;
      }
      // Method 2: If no environment variable, keep original URL
      // This means the worker will just add mobile parameters without changing domain
      
      // Add mobile parameter for mobile/tablet devices
      if (deviceType === "mobile" || deviceType === "tablet") {
        url.searchParams.set("m", "1");
      }
      
      // Create the request
      const modifiedRequest = new Request(url.toString(), {
        method: request.method,
        headers: request.headers,
        body: request.body,
        redirect: 'follow'
      });
      
      // Fetch and return the response directly
      const response = await fetch(modifiedRequest);
      return response;
      
    } catch (error) {
      console.error('Worker error:', error);
      
      // Fallback: return original request
      try {
        return await fetch(request);
      } catch (fallbackError) {
        return new Response('Service temporarily unavailable. Please try again.', {
          status: 503,
          headers: { 'Content-Type': 'text/plain' }
        });
      }
    }
  }
};

export default worker;
