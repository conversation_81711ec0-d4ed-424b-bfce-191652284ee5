/**
 * Cloudflare Worker for www.chouati.com
 * Proxies to mochou95.blogspot.com with mobile detection
 * Ready to use - no configuration needed!
 */

// Device Detection
const MOBILE_REGEX = /(?:phone|windows\s+phone|ipod|blackberry|(?:android|bb\d+|meego|silk|googlebot) .+? mobile|palm|windows\s+ce|opera\ mini|avantgo|mobilesafari|docomo|KAIOS|mobile|iphone)/i;
const TABLET_REGEX = /(?:ipad|playbook|(?:android|bb\d+|meego|silk)(?! .+? mobile)|tablet)/i;

const getDeviceType = (userAgent) => {
  if (typeof userAgent !== "string") return "desktop";
  
  if (MOBILE_REGEX.test(userAgent)) return "mobile";
  if (TABLET_REGEX.test(userAgent)) return "tablet";
  
  return "desktop";
};

// Main Worker
const worker = {
  async fetch(request, env, context) {
    try {
      // Get device type
      const userAgent = request.headers.get('User-Agent') || '';
      const deviceType = getDeviceType(userAgent);
      
      // Create the URL pointing to your actual Blogger site
      const url = new URL(request.url);
      
      // Redirect to your actual Blogger address
      url.hostname = 'mochou95.blogspot.com';
      
      // Add mobile parameter for mobile/tablet devices
      if (deviceType === "mobile" || deviceType === "tablet") {
        url.searchParams.set("m", "1");
      }
      
      // Create the request to your Blogger site
      const bloggerRequest = new Request(url.toString(), {
        method: request.method,
        headers: request.headers,
        body: request.body,
        redirect: 'follow'
      });
      
      // Fetch from your Blogger site and return the response
      const response = await fetch(bloggerRequest);
      return response;
      
    } catch (error) {
      console.error('Worker error:', error);
      
      // Fallback: try the original request
      try {
        return await fetch(request);
      } catch (fallbackError) {
        // Last resort: simple error page
        return new Response(`
          <html>
            <head><title>www.chouati.com - Temporarily Unavailable</title></head>
            <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
              <h1>Site Temporarily Unavailable</h1>
              <p>Please try refreshing the page in a moment.</p>
              <p><a href="https://mochou95.blogspot.com">Visit our blog directly</a></p>
            </body>
          </html>
        `, {
          status: 503,
          headers: {
            'Content-Type': 'text/html',
          }
        });
      }
    }
  }
};

export default worker;
