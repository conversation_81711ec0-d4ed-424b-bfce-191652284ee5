/**
 * Clean Cloudflare Worker for Blogger - Minimal Header Modification
 * Enhanced by chouati - v4.2 Clean
 * Fixes: Blogger verification issues, minimal header changes
 */

// Device Detection
const MOBILE_REGEX = /(?:phone|windows\s+phone|ipod|blackberry|(?:android|bb\d+|meego|silk|googlebot) .+? mobile|palm|windows\s+ce|opera\ mini|avantgo|mobilesafari|docomo|KAIOS|mobile|iphone)/i;
const TABLET_REGEX = /(?:ipad|playbook|(?:android|bb\d+|meego|silk)(?! .+? mobile)|tablet)/i;
const BOT_REGEX = /(?:googlebot|bingbot|slurp|duckduckbot|baiduspider|yandexbot|facebookexternalhit|twitterbot)/i;

// Configuration
const CONFIG = {
  BLOGGER_URL: 'mochou95.blogspot.com',
  RATE_LIMIT: {
    windowMs: 60000,
    maxRequests: 150,
  }
};

// Device detection
const getDeviceType = (userAgent) => {
  if (typeof userAgent !== "string") return "desktop";
  
  if (BOT_REGEX.test(userAgent)) return "bot";
  if (MOBILE_REGEX.test(userAgent)) return "mobile";
  if (TABLET_REGEX.test(userAgent)) return "tablet";
  
  return "desktop";
};

// Rate limiting
const rateLimiter = new Map();

const checkRateLimit = (clientIP) => {
  const now = Date.now();
  const { windowMs, maxRequests } = CONFIG.RATE_LIMIT;
  
  // Clean up old entries
  if (rateLimiter.size > 1000) {
    for (const [ip, data] of rateLimiter.entries()) {
      if (now > data.resetTime) {
        rateLimiter.delete(ip);
      }
    }
  }
  
  if (!rateLimiter.has(clientIP)) {
    rateLimiter.set(clientIP, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  const clientData = rateLimiter.get(clientIP);
  
  if (now > clientData.resetTime) {
    rateLimiter.set(clientIP, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (clientData.count >= maxRequests) {
    return false;
  }
  
  clientData.count++;
  return true;
};

// Main Worker Logic - Clean Version
const worker = {
  async fetch(request, env, context) {
    const startTime = Date.now();
    const clientIP = request.headers.get('CF-Connecting-IP') || request.headers.get('X-Forwarded-For') || 'unknown';
    const userAgent = request.headers.get('User-Agent') || '';
    const deviceType = getDeviceType(userAgent);
    
    try {
      // Rate limiting check
      if (!checkRateLimit(clientIP)) {
        return new Response('Rate limit exceeded. Please try again later.', { 
          status: 429,
          headers: {
            'Retry-After': '60',
            'Content-Type': 'text/plain'
          }
        });
      }
      
      // Handle OPTIONS requests
      if (request.method === 'OPTIONS') {
        return new Response(null, {
          status: 204,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
          }
        });
      }
      
      // Create the proxied URL
      const proxiedUrl = new URL(request.url);
      
      // Use environment variable or config for Blogger URL
      const bloggerUrl = env?.BLOGGER_URL || CONFIG.BLOGGER_URL;
      
      // Redirect to Blogger if not already on blogspot.com
      if (!proxiedUrl.hostname.includes('blogspot.com')) {
        proxiedUrl.hostname = bloggerUrl;
      }
      
      // Add mobile parameter for mobile/tablet devices
      if (deviceType === "mobile" || deviceType === "tablet") {
        proxiedUrl.searchParams.set("m", "1");
      }
      
      // Create the proxied request with MINIMAL header modifications
      // Keep original headers to avoid Blogger detection
      const proxiedRequest = new Request(proxiedUrl.toString(), {
        method: request.method,
        body: request.body,
        headers: request.headers, // Use original headers as-is
        redirect: "follow"
      });
      
      // Fetch the response
      let response = await fetch(proxiedRequest);
      
      // Return response with minimal modifications
      // Only add performance tracking header
      if (response) {
        const newHeaders = new Headers();
        
        // Copy all original response headers
        for (const [key, value] of response.headers.entries()) {
          newHeaders.set(key, value);
        }
        
        // Add only essential headers
        newHeaders.set('X-Response-Time', `${Date.now() - startTime}ms`);
        newHeaders.set('X-Device-Type', deviceType);
        
        return new Response(response.body, {
          status: response.status,
          statusText: response.statusText,
          headers: newHeaders
        });
      }
      
      return response;
      
    } catch (error) {
      console.error('Worker error:', error);
      
      // Simple fallback - try original request
      try {
        const fallbackResponse = await fetch(request);
        return fallbackResponse;
      } catch (fallbackError) {
        return new Response(`
          <!DOCTYPE html>
          <html>
          <head>
            <title>www.chouati.com - Service Unavailable</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
          </head>
          <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5;">
            <div style="max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
              <h1 style="color: #333; margin-bottom: 20px;">Service Temporarily Unavailable</h1>
              <p style="color: #666; margin-bottom: 20px;">We're experiencing a temporary issue. Please try again in a moment.</p>
              <p><a href="https://${CONFIG.BLOGGER_URL}" style="color: #007cba; text-decoration: none;">Visit our blog directly</a></p>
              <p><a href="javascript:location.reload()" style="color: #007cba; text-decoration: none;">Retry</a></p>
            </div>
          </body>
          </html>
        `, {
          status: 503,
          headers: {
            'Content-Type': 'text/html; charset=utf-8'
          }
        });
      }
    }
  }
};

export default worker;
