/**
 * Simplified <PERSON>flar<PERSON> Worker for Blogger - Safe Version
 * Enhanced by chouati - v3.0 Simple
 * This version focuses on core functionality without breaking your site
 */

// Device Detection
const MOBILE_REGEX = /(?:phone|windows\s+phone|ipod|blackberry|(?:android|bb\d+|meego|silk|googlebot) .+? mobile|palm|windows\s+ce|opera\ mini|avantgo|mobilesafari|docomo|KAIOS|mobile|iphone)/i;
const TABLET_REGEX = /(?:ipad|playbook|(?:android|bb\d+|meego|silk)(?! .+? mobile)|tablet)/i;
const BOT_REGEX = /(?:googlebot|bingbot|slurp|duckduckbot|baiduspider|yandexbot|facebookexternalhit|twitterbot)/i;

const getDeviceType = (userAgent) => {
  if (typeof userAgent !== "string") return "desktop";
  
  if (BOT_REGEX.test(userAgent)) return "bot";
  if (MOBILE_REGEX.test(userAgent)) return "mobile";
  if (TABLET_REGEX.test(userAgent)) return "tablet";
  
  return "desktop";
};

// Simple performance headers
const addPerformanceHeaders = (response, deviceType) => {
  const headers = new Headers(response.headers);
  
  // Basic security headers
  headers.set('X-Content-Type-Options', 'nosniff');
  headers.set('X-Frame-Options', 'SAMEORIGIN');
  headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Performance headers
  headers.set('X-Device-Type', deviceType);
  headers.set('X-Powered-By', 'Cloudflare-Worker');
  
  // Cache headers for static assets
  const url = response.url || '';
  if (url.match(/\.(css|js|jpg|jpeg|png|gif|webp|svg|ico|woff|woff2|ttf|eot)$/i)) {
    headers.set('Cache-Control', 'public, max-age=31536000'); // 1 year for assets
  } else {
    headers.set('Cache-Control', 'public, max-age=3600'); // 1 hour for HTML
  }
  
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers
  });
};

// Main Worker
const worker = {
  async fetch(request, env, context) {
    try {
      // Get device type
      const userAgent = request.headers.get('User-Agent') || '';
      const deviceType = getDeviceType(userAgent);
      
      // Handle CORS preflight
      if (request.method === 'OPTIONS') {
        return new Response(null, {
          status: 204,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
          }
        });
      }
      
      // Create the proxied URL
      const url = new URL(request.url);
      
      // IMPORTANT: Set your Blogger domain here
      // Replace 'YOUR_BLOG_NAME' with your actual blog name
      const BLOGGER_DOMAIN = env?.BLOGGER_URL || 'YOUR_BLOG_NAME.blogspot.com';
      
      // If using custom domain, redirect to Blogger
      if (!url.hostname.includes('blogspot.com')) {
        url.hostname = BLOGGER_DOMAIN;
      }
      
      // Add mobile parameter for mobile devices
      if (deviceType === "mobile" || deviceType === "tablet") {
        url.searchParams.set("m", "1");
      }
      
      // Create the proxied request
      const modifiedRequest = new Request(url.toString(), {
        method: request.method,
        headers: request.headers,
        body: request.body,
        redirect: 'follow'
      });
      
      // Fetch the response
      let response = await fetch(modifiedRequest);
      
      // Add performance headers
      response = addPerformanceHeaders(response, deviceType);
      
      return response;
      
    } catch (error) {
      console.error('Worker error:', error);
      
      // Fallback: return original request
      try {
        const fallbackResponse = await fetch(request);
        return fallbackResponse;
      } catch (fallbackError) {
        return new Response('Service temporarily unavailable', {
          status: 503,
          headers: {
            'Content-Type': 'text/plain',
            'Retry-After': '60'
          }
        });
      }
    }
  }
};

export default worker;
