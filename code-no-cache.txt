/**
 * Advanced Cloudflare Worker for Maximum Website Performance (No Cache Version)
 * Enhanced by chouati - v4.1 Stable
 * Features: Optimization, Security, Performance Analytics (Cache Disabled for Stability)
 */

// Enhanced Device Detection
const MOBILE_REGEX = /(?:phone|windows\s+phone|ipod|blackberry|(?:android|bb\d+|meego|silk|googlebot) .+? mobile|palm|windows\s+ce|opera\ mini|avantgo|mobilesafari|docomo|KAIOS|mobile|iphone)/i;
const TABLET_REGEX = /(?:ipad|playbook|(?:android|bb\d+|meego|silk)(?! .+? mobile)|tablet)/i;
const BOT_REGEX = /(?:googlebot|bingbot|slurp|duckduckbot|baiduspider|yandexbot|facebookexternalhit|twitterbot|rogerbot|linkedinbot|embedly|quora|showyoubot|outbrain|pinterest|slackbot|vkShare|W3C_Validator|whatsapp)/i;

// Configuration
const CONFIG = {
  BLOGGER_URL: 'mochou95.blogspot.com',

  SECURITY_HEADERS: {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'SAMEORIGIN',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  },

  PERFORMANCE_HEADERS: {
    'X-DNS-Prefetch-Control': 'on',
    'X-Powered-By': 'Cloudflare-Worker-Pro-v4.1',
  },

  RATE_LIMIT: {
    windowMs: 60000,
    maxRequests: 120,
  }
};

// Device detection
const getDeviceType = (userAgent) => {
  if (typeof userAgent !== "string") return "desktop";

  if (BOT_REGEX.test(userAgent)) return "bot";
  if (MOBILE_REGEX.test(userAgent)) return "mobile";
  if (TABLET_REGEX.test(userAgent)) return "tablet";

  return "desktop";
};

// Content type detection
const getContentType = (url) => {
  const pathname = new URL(url).pathname.toLowerCase();

  if (pathname.endsWith('.css')) return 'CSS';
  if (pathname.match(/\.(js|mjs)$/)) return 'JS';
  if (pathname.match(/\.(jpg|jpeg|png|gif|webp|svg|ico)$/)) return 'IMAGES';
  if (pathname.match(/\.(woff|woff2|ttf|eot)$/)) return 'FONTS';
  if (pathname.startsWith('/api/') || pathname.includes('json')) return 'API';

  return 'HTML';
};

// HTML optimization
const optimizeHTML = async (response) => {
  try {
    const html = await response.text();

    const minified = html
      .replace(/<!--[\s\S]*?-->/g, '')
      .replace(/\s+/g, ' ')
      .replace(/>\s+</g, '><')
      .replace(/\s+>/g, '>')
      .replace(/<\s+/g, '<')
      .trim();

    const newHeaders = new Headers();
    for (const [key, value] of response.headers.entries()) {
      newHeaders.set(key, value);
    }
    newHeaders.set('Content-Length', minified.length.toString());
    newHeaders.set('X-Optimized', 'HTML-Minified');

    return new Response(minified, {
      status: response.status,
      statusText: response.statusText,
      headers: newHeaders
    });
  } catch (error) {
    console.error('HTML optimization error:', error);
    return response;
  }
};

// CSS and JS optimization
const optimizeAssets = async (response, contentType) => {
  try {
    const content = await response.text();
    let optimized = content;

    if (contentType === 'CSS') {
      optimized = content
        .replace(/\/\*[\s\S]*?\*\//g, '')
        .replace(/\s+/g, ' ')
        .replace(/;\s*}/g, '}')
        .replace(/\s*{\s*/g, '{')
        .replace(/;\s*/g, ';')
        .replace(/,\s*/g, ',')
        .trim();
    } else if (contentType === 'JS') {
      optimized = content
        .replace(/\/\*[\s\S]*?\*\//g, '')
        .replace(/\/\/.*$/gm, '')
        .replace(/\s+/g, ' ')
        .replace(/;\s*}/g, '}')
        .trim();
    }

    const newHeaders = new Headers();
    for (const [key, value] of response.headers.entries()) {
      newHeaders.set(key, value);
    }
    newHeaders.set('Content-Length', optimized.length.toString());
    newHeaders.set('X-Optimized', `${contentType}-Minified`);

    return new Response(optimized, {
      status: response.status,
      statusText: response.statusText,
      headers: newHeaders
    });
  } catch (error) {
    console.error('Asset optimization error:', error);
    return response;
  }
};

// Enhanced headers
const enhanceHeaders = (response, deviceType, contentType) => {
  const newHeaders = new Headers();

  for (const [key, value] of response.headers.entries()) {
    newHeaders.set(key, value);
  }

  Object.entries(CONFIG.SECURITY_HEADERS).forEach(([key, value]) => {
    newHeaders.set(key, value);
  });

  Object.entries(CONFIG.PERFORMANCE_HEADERS).forEach(([key, value]) => {
    newHeaders.set(key, value);
  });

  newHeaders.set('X-Device-Type', deviceType);
  newHeaders.set('X-Content-Category', contentType);

  if (contentType === 'HTML') {
    const preloadLinks = [
      '</css/style.css>; rel=preload; as=style',
      '</js/main.js>; rel=preload; as=script',
      '<https://fonts.googleapis.com>; rel=dns-prefetch',
      '<https://www.google-analytics.com>; rel=dns-prefetch'
    ];
    newHeaders.set('Link', preloadLinks.join(', '));
  }

  if (contentType === 'API') {
    newHeaders.set('Access-Control-Allow-Origin', '*');
    newHeaders.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    newHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    newHeaders.set('Access-Control-Max-Age', '86400');
  }

  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: newHeaders
  });
};

// Rate limiting
const rateLimiter = new Map();

const checkRateLimit = (clientIP) => {
  const now = Date.now();
  const { windowMs, maxRequests } = CONFIG.RATE_LIMIT;

  if (rateLimiter.size > 1000) {
    for (const [ip, data] of rateLimiter.entries()) {
      if (now > data.resetTime) {
        rateLimiter.delete(ip);
      }
    }
  }

  if (!rateLimiter.has(clientIP)) {
    rateLimiter.set(clientIP, { count: 1, resetTime: now + windowMs });
    return true;
  }

  const clientData = rateLimiter.get(clientIP);

  if (now > clientData.resetTime) {
    rateLimiter.set(clientIP, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (clientData.count >= maxRequests) {
    return false;
  }

  clientData.count++;
  return true;
};

// Performance tracking
const trackPerformance = (request, response, startTime) => {
  const endTime = Date.now();
  const duration = endTime - startTime;

  const newHeaders = new Headers();
  for (const [key, value] of response.headers.entries()) {
    newHeaders.set(key, value);
  }

  newHeaders.set('X-Response-Time', `${duration}ms`);
  newHeaders.set('X-Timestamp', new Date().toISOString());

  console.log(`Performance: ${request.url} - ${duration}ms - ${response.status}`);

  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: newHeaders
  });
};

// Bot handling
const handleBotRequest = async (request, response) => {
  const userAgent = request.headers.get('User-Agent') || '';

  if (userAgent.includes('Googlebot') || userAgent.includes('Bingbot')) {
    const optimizedResponse = await optimizeHTML(response);

    const newHeaders = new Headers();
    for (const [key, value] of optimizedResponse.headers.entries()) {
      newHeaders.set(key, value);
    }
    newHeaders.set('X-Robots-Tag', 'index, follow');
    newHeaders.set('X-Bot-Optimized', 'true');

    return new Response(optimizedResponse.body, {
      status: optimizedResponse.status,
      statusText: optimizedResponse.statusText,
      headers: newHeaders
    });
  }

  return response;
};

// Main Worker Logic - No Cache Version (Completely Stable)
const worker = {
  async fetch(request, env, context) {
    const startTime = Date.now();
    const clientIP = request.headers.get('CF-Connecting-IP') || request.headers.get('X-Forwarded-For') || 'unknown';
    const userAgent = request.headers.get('User-Agent') || '';
    const deviceType = getDeviceType(userAgent);

    try {
      // Rate limiting check
      if (!checkRateLimit(clientIP)) {
        return new Response('Rate limit exceeded. Please try again later.', {
          status: 429,
          headers: {
            'Retry-After': '60',
            'X-Rate-Limit-Exceeded': 'true',
            'Content-Type': 'text/plain'
          }
        });
      }

      // Handle OPTIONS requests for CORS
      if (request.method === 'OPTIONS') {
        return new Response(null, {
          status: 204,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Max-Age': '86400',
          }
        });
      }

      // Determine content type
      const contentType = getContentType(request.url);

      // Create the proxied URL
      const proxiedUrl = new URL(request.url);

      // Use environment variable or config for Blogger URL
      const bloggerUrl = env?.BLOGGER_URL || CONFIG.BLOGGER_URL;

      // Redirect to Blogger if not already on blogspot.com
      if (!proxiedUrl.hostname.includes('blogspot.com')) {
        proxiedUrl.hostname = bloggerUrl;
      }

      // Add mobile parameter for mobile/tablet devices
      if (deviceType === "mobile" || deviceType === "tablet") {
        proxiedUrl.searchParams.set("m", "1");
      }

      // Create optimized request headers
      const requestHeaders = new Headers();
      for (const [key, value] of request.headers.entries()) {
        requestHeaders.set(key, value);
      }

      // Add worker identification
      requestHeaders.set('CF-Worker', 'v4.1-stable-no-cache');
      requestHeaders.set('X-Device-Type', deviceType);

      // Create the proxied request
      const proxiedRequest = new Request(proxiedUrl.toString(), {
        method: request.method,
        body: request.body,
        headers: requestHeaders,
        redirect: "follow"
      });

      // Fetch the response
      let response = await fetch(proxiedRequest);

      // Handle successful responses with optimization
      if (response && response.ok) {
        // Bot-specific handling
        if (deviceType === 'bot') {
          response = await handleBotRequest(request, response);
        }

        // Content optimization based on type
        switch (contentType) {
          case 'HTML':
            response = await optimizeHTML(response);
            break;
          case 'CSS':
          case 'JS':
            response = await optimizeAssets(response, contentType);
            break;
        }

        // Enhance headers for all responses
        response = enhanceHeaders(response, deviceType, contentType);
      }

      // Track performance and return
      return trackPerformance(request, response, startTime);

    } catch (error) {
      console.error('Worker error:', error);

      return new Response(`
        <!DOCTYPE html>
        <html>
        <head><title>www.chouati.com - Temporarily Unavailable</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
          <h1>Service Temporarily Unavailable</h1>
          <p>Please try again in a moment.</p>
          <p><a href="https://${CONFIG.BLOGGER_URL}">Visit our blog directly</a></p>
          <p><a href="javascript:location.reload()">Retry</a></p>
        </body>
        </html>
      `, {
        status: 503,
        headers: {
          'Content-Type': 'text/html',
          'X-Error': 'true'
        }
      });
    }
  }
};

export default worker;