/**
 * Minimal Cloudflare Worker for Blogger - Ultra Safe Version
 * This version focuses ONLY on device detection and proxying
 * No header modifications to avoid immutable header errors
 */

// Device Detection
const MOBILE_REGEX = /(?:phone|windows\s+phone|ipod|blackberry|(?:android|bb\d+|meego|silk|googlebot) .+? mobile|palm|windows\s+ce|opera\ mini|avantgo|mobilesafari|docomo|KAIOS|mobile|iphone)/i;
const TABLET_REGEX = /(?:ipad|playbook|(?:android|bb\d+|meego|silk)(?! .+? mobile)|tablet)/i;

const getDeviceType = (userAgent) => {
  if (typeof userAgent !== "string") return "desktop";
  
  if (MOBILE_REGEX.test(userAgent)) return "mobile";
  if (TABLET_REGEX.test(userAgent)) return "tablet";
  
  return "desktop";
};

// Main Worker - Ultra Simple
const worker = {
  async fetch(request, env, context) {
    try {
      // Get device type
      const userAgent = request.headers.get('User-Agent') || '';
      const deviceType = getDeviceType(userAgent);
      
      // Create the URL for your Blogger site
      const url = new URL(request.url);
      
      // REPLACE 'chouati' with your actual blog name
      // For www.chouati.com, your Blogger URL should be chouati.blogspot.com
      url.hostname = 'chouati.blogspot.com';
      
      // Add mobile parameter for mobile/tablet devices
      if (deviceType === "mobile" || deviceType === "tablet") {
        url.searchParams.set("m", "1");
      }
      
      // Create the request to Blogger
      const bloggerRequest = new Request(url.toString(), {
        method: request.method,
        headers: request.headers,
        body: request.body,
        redirect: 'follow'
      });
      
      // Fetch from Blogger and return the response directly
      const response = await fetch(bloggerRequest);
      
      // Return the response without any modifications
      return response;
      
    } catch (error) {
      console.error('Worker error:', error);
      
      // If anything fails, try to fetch the original request
      try {
        return await fetch(request);
      } catch (fallbackError) {
        // Last resort: return a simple error page
        return new Response(`
          <html>
            <head><title>Site Temporarily Unavailable</title></head>
            <body>
              <h1>Site Temporarily Unavailable</h1>
              <p>Please try again in a few moments.</p>
              <p>Error: ${error.message}</p>
            </body>
          </html>
        `, {
          status: 503,
          headers: {
            'Content-Type': 'text/html',
          }
        });
      }
    }
  }
};

export default worker;
